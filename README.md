<h1>Image Reprojection Error Calculation using ONNX in AWS Lambda</h1>

<p>
This AWS Lambda function calculates the reprojection error between two images using an ONNX model. The function processes the input images, runs inference using ONNX Runtime, and calculates the reprojection error.
</p>


<h2> Prerequisites</h2>

<ul>
<li>AWS SAM CLI</li>
<li>ONNX model (model.onnx)</li>
<li>Method to perform inference considering input/output and preprocessing/postprocessing functions</li>
</ul>


<h2> Installation </h2>

<h3> Serverless Application Model (SAM) </h3>


Follow the instructions in this site to download the AWS SAM-CLI:

https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html

For this example, we used the version: SAM CLI, version 1.121.0


<h3> Building the image </h3>


`<NAME_EMAIL>:fingermarkltd/camera_displacement.git`

`cd /camera_displacement/src`

Build the image locally using SAM:

`sam build`

Deploy the image locally:

`sam local invoke MyFunction --event event.json`

The output should be similar to the bellow:


> START RequestId: 872b396d-8760-47d5-9893-f6d0ef53d2a7 Version: $LATEST
{'reference_image_path': 'reference.png', 'captured_image_path': 'captured.png'}
Error 6.391793377286457
END RequestId: 98397d4a-7342-4149-adfb-59c74899c0e0
REPORT RequestId: 98397d4a-7342-4149-adfb-59c74899c0e0  Init Duration: 0.05 ms  Duration: 1959.31 ms    Billed Duration: 1960 ms        Memory Size: 2048 MB    Max Memory Used: 2048 MB
{"statusCode": 200, "body": "{\"result\": 6.391793377286457}"}
