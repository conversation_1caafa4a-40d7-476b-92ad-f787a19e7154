#!/usr/bin/env python3
"""
Simple test script to demonstrate Victoria Metrics cluster mode URL construction
and credential usage without external dependencies.
"""

import os
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

# Mock the external dependencies
class MockSlackAlertManager:
    pass

class MockWebClient:
    def __init__(self, token):
        self.token = token

# Mock the slack_sdk module
sys.modules['slack_sdk'] = type(sys)('slack_sdk')
sys.modules['slack_sdk'].WebClient = MockWebClient

# Mock loguru
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def success(self, msg): print(f"SUCCESS: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")

sys.modules['loguru'] = type(sys)('loguru')
sys.modules['loguru'].logger = MockLogger()

# Mock prometheus_client
sys.modules['prometheus_client'] = type(sys)('prometheus_client')
sys.modules['prometheus_client.exposition'] = type(sys)('exposition')
sys.modules['prometheus_client.exposition'].CONTENT_TYPE_LATEST = "application/openmetrics-text; version=1.0.0; charset=utf-8"

# Now import our metrics module
from metrics import MetricsGenerator

def test_victoria_metrics_cluster_mode():
    """Test Victoria Metrics cluster mode URL construction and credentials"""
    
    print("=== Victoria Metrics Cluster Mode Test ===\n")
    
    # Set up environment variables for cluster mode
    os.environ["METRICS_ENDPOINT"] = "https://victoria-metrics.ap-southeast-2.fingermark.tech"
    os.environ["METRICS_USERNAME"] = "ingester"  # Write account
    os.environ["METRICS_PASSWORD"] = "YAXHrbCRg43iPpEJTNbw"
    
    # Create metrics generator
    slack_manager = MockSlackAlertManager()
    metrics_gen = MetricsGenerator(slack_manager)
    
    print("1. Current URL Construction:")
    print(f"   Base endpoint: {metrics_gen.metrics_endpoint}")
    print(f"   Current insert URL: {metrics_gen.metrics_url}")
    print(f"   Expected insert URL: https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import")
    print()
    
    print("2. Current Credentials:")
    print(f"   Username: {metrics_gen.username}")
    print(f"   Password: {metrics_gen.password}")
    print()
    
    print("3. Query URL Construction (current behavior):")
    query_url = f"{metrics_gen.metrics_endpoint}/api/v1/query"
    query_range_url = f"{metrics_gen.metrics_endpoint}/api/v1/query_range"
    print(f"   Current query URL: {query_url}")
    print(f"   Current query_range URL: {query_range_url}")
    print(f"   Expected query URL: https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query")
    print(f"   Expected query_range URL: https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query_range")
    print()
    
    print("4. Cluster Mode Requirements:")
    print("   - Insert operations should use: /insert/0/prometheus/api/v1/import")
    print("   - Select operations should use: /select/0/prometheus/api/v1/query*")
    print("   - Insert operations should use 'ingester' credentials")
    print("   - Select operations should use 'querier' credentials")
    print()
    
    print("5. Test Metric Formatting:")
    metric = metrics_gen.format_prometheus_gauge(
        "camera_displacement_status",
        1,
        {"camera_id": "cam1", "site_id": "site1"}
    )
    print(f"   Formatted metric: {metric.strip()}")
    print()
    
    return True

if __name__ == "__main__":
    test_victoria_metrics_cluster_mode()
    print("Test completed successfully!")
