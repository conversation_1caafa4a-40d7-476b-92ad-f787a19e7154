service: eyecue-camera-displacement
useDotenv: true

custom:
  terraformCustomer:
    qa-aus: cv-qa-aus
    mcd-nzl: mnz-nzl
    default: ${env:ORGANIZATION}

  organization: ${self:custom.terraformCustomer.${env:ORGANI<PERSON>ATION}, self:custom.terraformCustomer.default}

  function-names:
    camera_displacement: eyecue-camera-displacement-lambda-${self:custom.organization}

provider:
  ecr:
    images:
      camera_displacement:
        path: ./
  name: aws
  runtime: python3.11
  stage: ${env:ENVIRONMENT, 'dev'}
  region: ${env:AWS_REGION, 'ap-southeast-2'}
  httpApi:
    cors: true
  stackTags:
    Product: "Eyecue"
    Customer: ${self:custom.organization}
    Terraform: "False"
    Stack: "CV"
    Serverless: "True"
    Environment: ${env:ENVIRONMENT, 'dev'}
    Application: ${self:service}
    Squad: "Vision"
    Customer Facing: "False"
    System: ${self:provider.runtime}

  environment:
    CUSTOMER: ${self:custom.organization}
    ENVIRONMENT: ${env:ENVIRONMENT, 'dev'}
    METRICS_ENDPOINT: ${env:METRICS_ENDPOINT, 'https://ec2-18-208-145-252.compute-1.amazonaws.com:8427'}
    METRICS_USERNAME: ${env:METRICS_USERNAME, 'camera-displacement'}
    METRICS_PASSWORD: ${env:METRICS_PASSWORD}
    SLACK_TOKEN: ${env:SLACK_TOKEN}
    SLACK_CHANNEL_ID: ${env:SLACK_CHANNEL_ID, 'C08E73HD0PR'}

  iam:
    role: arn:aws:iam::${env:AWS_ACCOUNT_ID}:role/eyecue-camera-displacement-lambda-role-${self:custom.organization}

functions:
  camera_displacement:
    memorySize: 4096
    timeout: 120
    name: ${self:custom.function-names.camera_displacement}
    image:
      name: camera_displacement
    tags:
      resource-name: ${self:custom.function-names.camera_displacement}
    logRetentionInDays: 365
    events:
      - sqs:
          arn: arn:aws:sqs:${env:AWS_REGION, 'ap-southeast-2'}:${env:AWS_ACCOUNT_ID}:eyecue-camera-displacement-sqs-${self:custom.organization}
          batchSize: 10
          maximumBatchingWindow: 60
          functionResponseType: ReportBatchItemFailures
      - httpApi:
          path: /slack-action
          method: any
    environment:
      API_GATEWAY_URL: !GetAtt HttpApi.ApiEndpoint

package:
  include:
    - src/**
  exclude:
    - ./**
