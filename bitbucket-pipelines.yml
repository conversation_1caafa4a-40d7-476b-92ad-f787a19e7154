definitions:
  variables:
    pipeVersion: &pipeVersion fingermarkltd/eyecue-serverless-pipeline:2.0.0

    defaultPipeVariables: &defaultPipeVariables
      ACCOUNT_TAG: ${ACCOUNT_TAG}
      ACCOUNTS_VERSION: ${SLS_PIPE_ACCOUNTS_VERSION}
      RUNTIME_LANGUAGE: python
      NODE_VERSION: "18"
      PYTHON_VERSION: "3.11"

  steps:
    - step: &deployAggregatedFunctionsStep
        name: Deploy camera displacement function
        oidc: true
        script:
          - pipe: *pipeVersion
            variables:
              <<: *defaultPipeVariables
              SLACK_TOKEN: ${SLACK_TOKEN}
              METRICS_ENDPOINT: ${METRICS_URL}
              METRICS_USER: ${METRICS_USER}
              METRICS_PASSWORD: ${METRICS_PASSWORD}
pipelines:
  custom:
    deploy-all-functions:
      - variables:
          - name: ACCOUNT_TAG
            default: qa-aus
      - step: *deployAggregatedFunctionsStep
