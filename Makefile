SOURCE_PROFILE ?= default
PROFILE ?= camera-displacement
ACCOUNT ?= ************
ASSUME_ROLE ?= arn:aws:iam::$(ACCOUNT):role
ACCESS ?= AdminAccess
FUNCTION=authorizer

assume_role:
	@echo "Assuming role to account: $(ASSUME_ROLE) using access $(ACCESS)"
	@aws sts assume-role \
		--profile $(SOURCE_PROFILE) \
		--output text \
		--role-arn $(ASSUME_ROLE)/$(ACCESS) \
		--role-session-name $(PROFILE)-assumed \
	| tail -1 \
	| awk 'BEGIN{ cmd="aws --profile=$(PROFILE) configure set " } { \
		print cmd "aws_access_key_id " $$2 "\n" \
			cmd "aws_secret_access_key " $$4 "\n" \
			cmd "aws_session_token " $$5 }' \
	| xargs -0 /bin/bash -c
	@sed -ibak -n '/aws_security_token/!p' ~/.aws/credentials
	@awk '{ if ("aws_session_token"==$$1) print $$0 "\naws_security_token = " $$3; else print $$0; }' ~/.aws/credentials > ~/.aws/credentials.tmp
	@mv ~/.aws/credentials.tmp ~/.aws/credentials

deploy-dev: assume_role
	@echo "Deploying to dev"
	@ORGANISATION=cv-dev sls deploy --stage dev --aws-profile $(PROFILE) --verbose


sls-logs-dev: assume_role
	@echo "Getting logs for dev"
	@ORGANISATION=cv-dev sls logs --stage dev --aws-profile $(PROFILE) --function $(FUNCTION) --tail