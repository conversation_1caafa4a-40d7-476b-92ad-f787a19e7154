import cv2
import numpy as np
import requests
from loguru import logger
from typing import <PERSON><PERSON>
from urllib.parse import urlparse
from model_loader import ModelLoader


class ModelInference:
    def __init__(self):
        """
        Initialize the ModelInference class and load the ONNX model.
        """
        self.model_loader = ModelLoader(
            model_path="model.onnx", providers=["CPUExecutionProvider"]
        )

    def get_rotation_scale(self, H: np.ndarray) -> <PERSON><PERSON>[np.ndarray, float]:
        """
        Extracts the rotation matrix and scale factor from a homography matrix.

        Args:
            H (np.ndarray): A 3x3 homography matrix.

        Returns:
            Tuple[np.ndarray, float]: Rotation matrix and scale factor.
        """
        H_2x2 = H[:2, :2]
        U, S, Vt = np.linalg.svd(H_2x2)
        R = np.dot(U, Vt)
        scale = np.mean(S)
        return R, scale

    def get_reprojection_error(
        self, H: np.ndarray, n0: np.ndarray, n1: np.ndarray
    ) -> np.ndarray:
        """
        Calculate the reprojection error between two sets of points.

        Args:
            H (np.ndarray): Homography matrix.
            n0 (np.ndarray): Original points.
            n1 (np.ndarray): Transformed points.

        Returns:
            np.ndarray: Array of reprojection errors.
        """
        ones_column = np.ones((n0.shape[0], 1))
        n0_ex = np.hstack((n0, ones_column))
        k0_transformed_homogeneous = np.dot(H, n0_ex.T).T
        if np.any(k0_transformed_homogeneous[:, 2].reshape(-1, 1) == 0):
            return np.finfo(
                np.float32
            ).max  # return a very big error or handle the error in some other way
        k0_transformed = k0_transformed_homogeneous[:, :2] / k0_transformed_homogeneous[
            :, 2
        ].reshape(-1, 1)
        errors = np.linalg.norm(n1 - k0_transformed, axis=1)
        return errors

    def get_image_info(
        self, image0: np.ndarray, image1: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Process two images to prepare them for model inference and get keypoints, matches, and scores.

        Args:
            image0 (np.ndarray): First image.
            image1 (np.ndarray): Second image.

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: Keypoints, matches, and match scores.
        """
        image0 = cv2.cvtColor(image0, cv2.COLOR_BGR2GRAY)
        image1 = cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY)

        image0 = cv2.resize(image0, (1024, 1024), interpolation=cv2.INTER_AREA)
        image1 = cv2.resize(image1, (1024, 1024), interpolation=cv2.INTER_AREA)

        image0 = np.expand_dims(image0, axis=0) / 255.0
        image1 = np.expand_dims(image1, axis=0) / 255.0

        input_data = np.stack((image0, image1), axis=0).astype(np.float32)
        self.onnx_model_session = self.model_loader.get_session()
        keypoints, matches, mscores = self.onnx_model_session.run(
            None, {"images": input_data}
        )
        return keypoints, matches, mscores

    def validate_result(self, result):
        """
        Validate a single result against the defined thresholds.

        Args:
            result (dict): A dictionary containing the result to validate.
            camera_id (str): Camera Id.
        Returns:
            dict: The original result with an additional field 'status' ('Accepted' or 'Rejected').
        """
        thresholds = {
            "angle": (0, 0.01),  # Range: 0 ± 0.01
            "scale": (1, 0.11),  # Range: 1 ± 0.11
            "reprojection_error": 15,  # Max: < 15
            "percentage": 0.25,  # Min: > 0.25
        }

        is_angle_valid = abs(result["angle"]) <= thresholds["angle"][1]
        is_scale_valid = (
            abs(result["scale"] - thresholds["scale"][0]) <= thresholds["scale"][1]
        )
        is_reprojection_error_valid = (
            result["reprojection_error"] < thresholds["reprojection_error"]
        )
        is_percentage_valid = result["percentage"] > thresholds["percentage"]

        status = (
            "Accepted"
            if all(
                [
                    is_angle_valid,
                    is_scale_valid,
                    is_reprojection_error_valid,
                    is_percentage_valid,
                ]
            )
            else "Rejected"
        )

        result["status"] = status

        return result

    def get_inference_results(
        self, ref_img_path: str, captured_img_path: str, messageId: str
    ) -> dict:
        """
        Perform inference on a pair of images and return the results.

        Args:
            ref_img_path (str): Path or URL to the reference image.
            captured_img_path (str): Path or URL to the captured image.
            messageId (str): Identifier for the message.
        Returns:
            dict: The result of the inference.
        """
        reference_img = self.load_image(ref_img_path)
        captured_img = self.load_image(captured_img_path)

        if reference_img is None or captured_img is None:
            return {"messageId": messageId, "error": "Image loading failed"}

        keypoints, matches, _ = self.get_image_info(reference_img, captured_img)

        # Minimum required to assemble the homography matrix (3x3), which has 8 degrees of freedom.
        # Each match provides 2 constraints (x and y), so at least 4 matches are needed.
        if len(matches) >= 4:
            mk0 = keypoints[0][matches[..., 1]]
            mk1 = keypoints[1][matches[..., 2]]

            H, mask = cv2.findHomography(
                mk0.astype(np.float32), mk1.astype(np.float32), cv2.RANSAC
            )
            perc_homography = np.sum(mask) / len(matches)
            R, scale = self.get_rotation_scale(H)
            theta = np.arctan2(R[1, 0], R[0, 0])
            errors = self.get_reprojection_error(H, mk0, mk1)
            result_for_validation = {
                "messageId": messageId,
                "reprojection_error": np.mean(errors),
                "angle": theta,
                "scale": scale,
                "percentage": perc_homography,
            }
            result = self.validate_result(result_for_validation)
        else:
            result = {"messageId": messageId, "error": "No matches"}

        return result

    @staticmethod
    def load_image(path: str) -> np.ndarray:
        """
        Load an image from a URL or a local file path.

        Args:
            path (str): The URL or file path to the image.

        Returns:
            np.ndarray: The loaded image, or None if loading fails.
        """
        if ModelInference.is_url(path):
            return ModelInference.load_image_from_url(path)
        else:
            return ModelInference.load_image_from_file(path)

    @staticmethod
    def is_url(path: str) -> bool:
        """
        Check if the provided path is a URL.

        Args:
            path (str): Path to check.

        Returns:
            bool: True if the path is a URL, False otherwise.
        """
        try:
            result = urlparse(path)
            return all([result.scheme, result.netloc])
        except Exception as e:
            logger.error(f"Error determining if path is URL: {e}")
            return False

    @staticmethod
    def load_image_from_url(url: str) -> np.ndarray:
        """
        Load an image from a URL.

        Args:
            url (str): The URL pointing to the image.

        Returns:
            np.ndarray: The loaded image or None if an error occurs.
        """
        try:
            response = requests.get(url)
            response.raise_for_status()
            image_array = np.asarray(bytearray(response.content), dtype=np.uint8)
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            logger.error(f"Error loading image from URL: {e}")
            return None

    @staticmethod
    def load_image_from_file(path: str) -> np.ndarray:
        """
        Load an image from a local file path.

        Args:
            path (str): Path to the local image file.

        Returns:
            np.ndarray: The loaded image or None if an error occurs.
        """
        try:
            image = cv2.imread(path, cv2.IMREAD_COLOR)
            if image is None:
                raise ValueError("Image could not be loaded.")
            return image
        except Exception as e:
            logger.error(f"Error loading image from file: {e}")
            return None
