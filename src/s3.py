import boto3
import urllib.parse
import requests
from loguru import logger


def upload_image_to_s3(upload_img_path: str, captured_img_path: str):
    """
    Downloads an image using a presigned S3 URL and uploads it to a new location in S3.

    :param upload_img_path: The full S3 URL where the image will be uploaded.
    :param captured_img_path: The full S3 presigned URL from which the image will be downloaded.
    """
    s3 = boto3.client("s3")

    def parse_s3_url(s3_url):
        parsed = urllib.parse.urlparse(s3_url)
        bucket = parsed.netloc.split(".")[0]  # Extract bucket name
        key = parsed.path.lstrip("/")  # Extract key
        return bucket, key

    try:
        response = requests.get(captured_img_path, stream=True)
        response.raise_for_status()  # Raise an error for HTTP failures

        dest_bucket, dest_key = parse_s3_url(upload_img_path)

        # Upload the downloaded image to S3
        s3.put_object(Bucket=dest_bucket, Key=dest_key, Body=response.content)

        logger.info(f"Successfully uploaded {captured_img_path} to {upload_img_path}")
    except Exception as e:
        logger.error(f"Error processing image transfer: {e}")
        raise
