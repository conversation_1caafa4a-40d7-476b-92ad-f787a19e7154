AWSTemplateFormatVersion: "2010-09-09"
Transform: "AWS::Serverless-2016-10-31"
Resources:
  MyFunction:
    Type: "AWS::Serverless::Function"
    Properties:
      Handler: lambda_function.lambda_handler
      Runtime: python3.11
      CodeUri: .
      MemorySize: 2048
      Timeout: 30
      #Environment:
      #  Variables:
      #    PARAM1: VALUE1
      #Policies:
      #  - AWSLambdaBasicExecutionRole
      #  - S3ReadPolicy:
      #      BucketName: your-s3-bucket-name
