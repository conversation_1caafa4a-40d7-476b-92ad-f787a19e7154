from loguru import logger
import onnxruntime as ort


class ModelLoader:
    def __init__(self, model_path="model.onnx", providers=None):
        """
        Initialize the ONNX model loader.

        Args:
            model_path (str): Path to the ONNX model file. Default is 'model.onnx'.
            providers (list): List of execution providers for ONNX. Default is ["CPUExecutionProvider"].
        """
        self.model_path = model_path
        self.providers = providers or ["CPUExecutionProvider"]
        self.session = None

    def get_session(self):
        """
        Load the ONNX model session. If already loaded, reuse the existing session.

        Returns:
            ort.InferenceSession: The ONNX runtime session for the model.

        Raises:
            RuntimeError: If the model cannot be loaded.
        """
        if self.session is not None:
            logger.info("Using the already loaded ONNX model session.")
            return self.session

        try:
            logger.info(
                f"Loading ONNX model from '{self.model_path}' with providers: {self.providers}"
            )
            sess_options = ort.SessionOptions()
            self.session = ort.InferenceSession(
                self.model_path, sess_options, self.providers
            )
            logger.info("ONNX model successfully loaded.")
        except Exception as e:
            logger.error(f"Failed to load ONNX model: {e}")
            raise RuntimeError("Could not load the ONNX model.") from e

        return self.session
