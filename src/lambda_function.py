import json
from aws_lambda_typing import context as context_, events

from loguru import logger

from metrics import MetricsGenerator
from model_inference import ModelInference
from s3 import upload_image_to_s3
from slack_alert_manager import SlackAlertManager

processor = ModelInference()
slack_alert_manager = SlackAlertManager()
metrics = MetricsGenerator(slack_alert_manager)


def handle_sqs_event(event):
    """
    Handle SQS event and calculate camera displacement

    Args:
        event: Lambda event data - a SQS event from the camera displacement service

    Returns:
        dict: Response with the calculated reprojection error for each message.
    """
    results = []

    for record in event["Records"]:
        try:
            logger.info(f"Processing record with MessageId: {record['messageId']}")
            input_data = json.loads(record["body"])
            logger.debug(f"Input data: {input_data}")

            ref_img_path = input_data.get("reference_image_path")
            captured_img_path = input_data.get("captured_image_path")
            camera_id = input_data.get("camera_id")
            site_id = input_data.get("site_id")
            roi_types = input_data.get("roi_types", "Unknown")
            roi_ids = input_data.get("roi_ids", "Unknown")

            if not (ref_img_path and captured_img_path and camera_id and site_id):
                logger.error(
                    f"Missing required fields in input_data: {input_data}",
                    exc_info=True,
                )
                results.append(
                    {
                        "messageId": record["messageId"],
                        "error": f"Value Error: Missing required fields in input_data: {input_data}",
                    }
                )

            logger.info(f"Running inference for MessageId: {record['messageId']}")
            result = processor.get_inference_results(
                ref_img_path=ref_img_path,
                captured_img_path=captured_img_path,
                messageId=record["messageId"],
            )

            result["camera_id"] = camera_id
            result["site_id"] = site_id
            result["ref_img_path"] = ref_img_path
            result["captured_img_path"] = captured_img_path
            result["roi_types"] = roi_types
            result["roi_ids"] = roi_ids
            logger.success(f"Inference result: {result}")

            results.append(
                {"messageId": record["messageId"], "MessageBody": json.dumps(result)}
            )

        except Exception as e:
            logger.error(
                f"Error processing MessageId {record['messageId']}: {e}", exc_info=True
            )

        try:
            metrics.update_displacement_prometheus_gauge(result)
        except Exception as e:
            logger.error(
                f"Error update_displacement_prometheus_gauge MessageId {record['messageId']}: {e}",
                exc_info=True,
            )
            results.append({"messageId": record["messageId"], "error": str(e)})
        try:
            metrics.check_camera_displacement(result)
        except Exception as e:
            logger.error(
                f"Error check_camera_displacement MessageId {record['messageId']}: {e}",
                exc_info=True,
            )
            results.append({"messageId": record["messageId"], "error": str(e)})

    response = {
        "statusCode": 200,
        "body": json.dumps(results),
    }
    logger.info(f"Lambda execution completed. Response: {response}")
    return response


def handle_http_event(event) -> dict:
    """
    Handle HTTP event and upload image to S3 based on the given event

    Args:
        event: Lambda event data - a http (API Gateway) event from slack

    Returns:
        dict: Response from the upload execution
    """
    params = event.get("queryStringParameters")
    upload_img_path = params.get("upload_img_path")
    if not upload_img_path:
        return {"statusCode": 400, "body": "Missing 'upload_img_path'"}
    captured_img_path = params.get("captured_img_path")
    if not captured_img_path:
        return {"statusCode": 400, "body": "Missing 'captured_img_path'"}

    upload_image_to_s3(upload_img_path, captured_img_path)

    slack_alert_manager.send_s3_update_alert(upload_img_path=upload_img_path)

    return {"statusCode": 200, "body": "Successfully replaced displacement image."}


def lambda_handler(event: events.SQSEvent, context: context_.Context) -> dict:
    """
    AWS Lambda handler function to process images and calculate reprojection error.

    Args:
        event: Lambda event data, expected to be an SQS event, or a http (API Gateway) event from slack
        context (context_.Context): Lambda context.

    Returns:
        dict: Response from the event processing
    """
    logger.info("Lambda handler started.")
    logger.info(f"Event received: {json.dumps(event)}")

    # Detect if this is an HTTP event (API Gateway) from slack
    if event.get("rawPath") == "/slack-action":
        return handle_http_event(event)

    # Otherwise assume it's an SQS event
    if "Records" in event and event["Records"][0].get("eventSource") == "aws:sqs":
        return handle_sqs_event(event)

    logger.warning("Unsupported event type.")
    return {"statusCode": 400, "body": "Unsupported event type"}
