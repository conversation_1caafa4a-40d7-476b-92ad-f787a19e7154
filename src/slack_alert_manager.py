import json
import os
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
from loguru import logger
import urllib


class SlackAlertManager:
    def __init__(self):
        self.slack_token = os.environ.get("SLACK_TOKEN")
        self.channel_id = os.environ.get("SLACK_CHANNEL_ID", "C08E73HD0PR")
        self.api_gateway_endpoint = os.environ.get("API_GATEWAY_URL")

        logger.info(
            f"SlackAlertManager init successfully (SLACK_TOKEN: {len(self.slack_token)}, SLACK_CHANNEL_ID: {self.channel_id })"
        )
        if not self.slack_token:
            logger.error(
                "SLACK_TOKEN is missing. Please set it in the environment variables."
            )
            raise ValueError("SLACK_TOKEN is required.")

        if not self.channel_id:
            logger.error(
                "SLACK_CHANNEL_ID is missing. Please set it in the environment variables."
            )
            raise ValueError("SLACK_CHANNEL_ID is required.")

        self.client = WebClient(token=self.slack_token)

    def send_alert(
        self, camera_id, site_id, ref_img_path, captured_img_path, roi_types, roi_ids
    ):
        """
        Sends a structured message to Slack when a camera displacement is detected.
        """
        blocks = [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": "*Camera Displacement Detected!*",
                },
            },
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": "*Store Info:*"},
            },
            {
                "type": "section",
                "fields": [
                    {"type": "mrkdwn", "text": f"📍 *Site ID:* `{site_id}`"},
                    {"type": "mrkdwn", "text": f"📷 *Camera ID:* `{camera_id}`"},
                    {"type": "mrkdwn", "text": f"🚗 *ROI Types:* `{roi_types}`"},
                    {"type": "mrkdwn", "text": f"🚗 *ROI IDs:* `{roi_ids}`"},
                ],
            },
            {
                "type": "section",
                "text": {"type": "mrkdwn", "text": "*Image Links:*"},
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Reference Image:* <{ref_img_path}|Click to view>\n"
                    f"*Captured Image:* <{captured_img_path}|Click to view>",
                },
            },
            {"type": "divider"},
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": "_Generated automatically by Eyecue System_",
                    }
                ],
            },
        ]

        payload_data = {
            "upload_img_path": ref_img_path,
            "captured_img_path": captured_img_path,
        }
        encoded_payload = urllib.parse.urlencode(payload_data)

        api_url = f"{self.api_gateway_endpoint}/slack-action?{encoded_payload}"

        blocks.append(
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "Replace Image in S3",
                        },
                        "style": "danger",
                        "action_id": "process_image",
                        "url": api_url,
                    }
                ],
            }
        )

        payload = {
            "text": "*Camera Displacement Alert*",
            "blocks": blocks,
        }

        try:
            response = self.client.chat_postMessage(
                channel=self.channel_id, text=payload["text"], blocks=payload["blocks"]
            )

            message_timestamp = response["ts"]
            logger.info(
                f"Slack alert sent successfully (Channel: {self.channel_id}, TS: {message_timestamp})"
            )
            return {"status": "success", "ts": message_timestamp}

        except SlackApiError as e:
            logger.error(f"Failed to send alert to Slack: {e.response['error']}")
            return {"status": "failure", "error": e.response["error"]}

        except Exception as e:
            logger.error(f"Unexpected error: {e}", exc_info=True)
            return {"status": "error", "message": str(e)}

    def send_s3_update_alert(self, upload_img_path):
        """
        Sends a Slack alert after an image is uploaded to S3.

        :param upload_img_path: S3 path where the image was uploaded.
        """
        payload = {
            "text": "*S3 Image Update Alert*",
            "blocks": [
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": "*An image has been updated in S3!*",
                    },
                },
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"🆕 *Updated Image:* <{upload_img_path}|View>",
                        },
                    ],
                },
                {"type": "divider"},
                {
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": "_Generated automatically by Eyecue System_",
                        }
                    ],
                },
            ],
        }

        try:
            response = self.client.chat_postMessage(
                channel=self.channel_id, text=payload["text"], blocks=payload["blocks"]
            )

            message_timestamp = response["ts"]
            logger.info(
                f"Slack alert sent successfully (Channel: {self.channel_id}, TS: {message_timestamp})"
            )
            return {"status": "success", "ts": message_timestamp}

        except SlackApiError as e:
            logger.error(f"Failed to send alert to Slack: {e.response['error']}")
            return {"status": "failure", "error": e.response["error"]}

        except Exception as e:
            logger.error(f"Unexpected error: {e}", exc_info=True)
            return {"status": "error", "message": str(e)}


if __name__ == "__main__":
    slack_sender = SlackAlertManager()
    response = slack_sender.send_alert(
        camera_id="CAM123",
        site_id="SITE456",
        ref_img_path="https://test.com/ref_image.jpg",
        captured_img_path="https://test.com/captured_image.jpg",
    )
    print(response)
