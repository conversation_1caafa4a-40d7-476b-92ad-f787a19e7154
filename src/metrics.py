import os
import datetime
import requests
from prometheus_client.exposition import CONTENT_TYPE_LATEST
from loguru import logger


class MetricsGenerator:
    def __init__(self, slack_alert_manager):
        self.slack_alert_manager = slack_alert_manager
        self.metrics_endpoint = os.environ.get(
            "METRICS_ENDPOINT", "http://localhost:8428"
        )
        self.username = os.environ.get("METRICS_USERNAME", None)
        self.password = os.environ.get("METRICS_PASSWORD", None)
        self.alert_frequency = int(
            os.environ.get("ALERT_FREQUENCY", 1)
        )  # Alert frequency in days
        self.alert_threshold = int(
            os.environ.get("ALERT_THRESHOLD", 24)
        )  # Alert threshold in hours
        self.metrics_url = f"{self.metrics_endpoint}/api/v1/import/prometheus"

    @staticmethod
    def format_prometheus_gauge(
        metric_name: str, metric_value: int | float, tags: dict[str, str]
    ) -> str:
        """
        Formats a Prometheus-compatible metric string with tags and metric value.
        Args:
            metric_name (str): The name of the metric.
            metric_value (Union[int, float]): Numeric value for the metric.
            tags (Dict[str, str]): Dictionary of tags to include in the metric.
        Returns:
            str: A Prometheus-compatible metric string.
        """
        tags_string = ",".join([f'{key}="{value}"' for key, value in tags.items()])
        return f"{metric_name}{{{tags_string}}} {metric_value}\n"

    def get_from_prometheus(self, query_url: str, params: dict) -> dict:
        response = requests.get(
            query_url,
            params=params,
            auth=(
                (self.username, self.password)
                if self.username and self.password
                else None
            ),
            timeout=60,
            verify=False,
        )
        response.raise_for_status()
        data = response.json()
        return data

    def post_to_prometheus(self, metric: str) -> dict:
        try:
            response = requests.post(
                self.metrics_url,
                data=metric,
                headers={"Content-Type": CONTENT_TYPE_LATEST},
                auth=(
                    (self.username, self.password)
                    if self.username and self.password
                    else None
                ),
                timeout=60,
                verify=False,
            )
            response.raise_for_status()
            logger.success(f"Metric successfully sent: {metric.strip()}")
            return {"statusCode": 200, "body": "OK"}
        except requests.exceptions.RequestException as e:
            logger.error(f"Error sending metric: {e}")
            return {"statusCode": 500, "body": str(e)}

    def update_alert_prometheus_gauge(self, results: dict):
        """
        Updates Camera Displacment alert prometheus gauge metrics by sending data to VictoriaMetrics.
        This data is used to identify the last alert time.
        Args:
            results (dict): Dictionary containing metric information.
        Returns:
            dict: Status of the operation.
        """
        timestamp = int(datetime.datetime.now().timestamp())
        metric = self.format_prometheus_gauge(
            "camera_displacement_alert",
            timestamp,
            {
                "camera_id": results["camera_id"],
                "site_id": results["site_id"],
            },
        )
        return self.post_to_prometheus(metric)

    def update_displacement_prometheus_gauge(self, results: dict):
        """
        Updates Camera Displacment prometheus gauge metrics by sending data to VictoriaMetrics.
        Args:
            results (dict): Dictionary containing metric information.
        Returns:
            dict: Status of the operation.
        """
        binary_status = 0 if results["status"] == "Accepted" else 1
        metric = self.format_prometheus_gauge(
            "camera_displacement_status",
            binary_status,
            {
                "camera_id": results["camera_id"],
                "site_id": results["site_id"],
            },
        )
        return self.post_to_prometheus(metric)

    def should_send_alert(self, camera_id: str, site_id: str) -> bool:
        """
        Checks if alert has been sent within the specified frequency of days from prometheus.
        If an alert should be sent, returns True. False otherwise.
        """
        promql_query = f'last_over_time(camera_displacement_alert{{camera_id="{camera_id}", site_id="{site_id}"}}[30d])'
        query_url = f"{self.metrics_endpoint}/api/v1/query_range"
        params = {"query": promql_query}
        try:
            data = self.get_from_prometheus(query_url, params)
            if "data" in data and "result" in data["data"] and data["data"]["result"]:
                last_alert_timestamp = int(
                    max(data["data"]["result"][0]["values"], key=lambda x: int(x[1]))[1]
                )
                current_timestamp = int(datetime.datetime.now().timestamp())
                return current_timestamp >= (
                    last_alert_timestamp + (self.alert_frequency * 60 * 60 * 24)
                )
            else:
                return True
        except requests.exceptions.RequestException as e:
            logger.error(f"Error querying last alert timestamp: {e}")
            return True

    def check_camera_displacement(self, results: dict):
        """
        Queries VictoriaMetrics to check if the camera displacement exceeded the threshold in the last 24 hours.
        """
        camera_id = results["camera_id"]
        site_id = results["site_id"]
        ref_img_path = results["ref_img_path"]
        captured_img_path = results["captured_img_path"]
        roi_types = results["roi_types"]
        roi_ids = results["roi_ids"]

        promql_query = f'sum_over_time(camera_displacement_status{{camera_id="{camera_id}", site_id="{site_id}"}}[24h])'
        query_url = f"{self.metrics_endpoint}/api/v1/query"
        params = {"query": promql_query}

        try:
            data = self.get_from_prometheus(query_url, params)

            if "data" in data and "result" in data["data"] and data["data"]["result"]:
                displacement_sum = float(data["data"]["result"][0]["value"][1])
                logger.info(
                    f"Total displacement events in last 24h for camera {camera_id} at site {site_id}: {displacement_sum}"
                )
                if displacement_sum >= self.alert_threshold:
                    if not self.should_send_alert(camera_id, site_id):
                        logger.info(
                            f"Alert already sent for camera {camera_id} at site {site_id}. Skipping alert."
                        )
                        return True
                    try:
                        message_response = self.slack_alert_manager.send_alert(
                            camera_id=camera_id,
                            site_id=site_id,
                            ref_img_path=ref_img_path,
                            captured_img_path=captured_img_path,
                            roi_types=roi_types,
                            roi_ids=roi_ids,
                        )
                        logger.success(
                            f"Slack alert successfully sent: {message_response}"
                        )
                        self.update_alert_prometheus_gauge(results)
                    except Exception as e:
                        logger.error(f"Error creating Slack message: {e}")
                    return True
            else:
                logger.warning(
                    f"No data received for camera {camera_id} at site {site_id}."
                )
        except requests.exceptions.RequestException as e:
            logger.error(f"Error querying VictoriaMetrics: {e}")
        return False
