{"Records": [{"messageId": "1", "receiptHandle": "SomeReceiptHandle", "body": "{ \"camera_id\": \"152\", \"site_id\": \"fm-tst-aus-0318\", \"reference_image_path\": \"reference.jpg\", \"captured_image_path\": \"captured_accepted.jpg\" }", "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "0", "SenderId": "SomeSenderId", "ApproximateFirstReceiveTimestamp": "0"}, "messageAttributes": {}, "md5OfBody": "SomeMD5", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:region:account-id:queue-name", "awsRegion": "region"}, {"messageId": "2", "receiptHandle": "SomeReceiptHandle", "body": "{ \"camera_id\": \"152\", \"site_id\": \"fm-tst-aus-0318\", \"reference_image_path\": \"reference.jpg\", \"captured_image_path\": \"captured_not_accepted.jpg\" }", "attributes": {"ApproximateReceiveCount": "1", "SentTimestamp": "0", "SenderId": "SomeSenderId", "ApproximateFirstReceiveTimestamp": "0"}, "messageAttributes": {}, "md5OfBody": "SomeMD5", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:region:account-id:queue-name", "awsRegion": "region"}]}