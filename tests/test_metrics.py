import os
import unittest
from unittest.mock import patch, MagicMock
import sys
import time

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../src")))
from metrics import MetricsGenerator
from slack_alert_manager import SlackAlertManager


class TestMetricsGenerator(unittest.TestCase):

    def setUp(self):
        os.environ["SLACK_TOKEN"] = "test"

        slack_alert_manager = SlackAlertManager()
        self.metrics_gen = MetricsGenerator(slack_alert_manager)

    @staticmethod
    def get_timestamps_minus_hours(hours=1) -> int:
        current_time = int(time.time())  # Get the current Unix timestamp in seconds
        timestamp = current_time - (hours * 60 * 60)  # Subtract hours
        return timestamp

    @patch("requests.get")
    def test_get_from_prometheus(self, mock_get):
        mock_response = MagicMock()
        mock_response.json.return_value = {"data": "test_data"}
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        response = self.metrics_gen.get_from_prometheus("http://test-url", {})
        self.assertEqual(response, {"data": "test_data"})
        mock_get.assert_called_once()

    @patch("requests.post")
    def test_post_to_prometheus_success(self, mock_post):
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        result = self.metrics_gen.post_to_prometheus("metric_data")
        self.assertEqual(result, {"statusCode": 200, "body": "OK"})
        mock_post.assert_called_once()

    def test_format_prometheus_gauge(self):
        result = self.metrics_gen.format_prometheus_gauge(
            "test_metric", 100, {"tag1": "value1"}
        )
        self.assertEqual(result, 'test_metric{tag1="value1"} 100\n')

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    def test_should_send_alert(self, mock_get_from_prometheus):
        mock_get_from_prometheus.return_value = {
            "data": {"result": [{"values": [["timestamp", "1700000000"]]}]}
        }

        result = self.metrics_gen.should_send_alert("cam1", "site1")
        self.assertTrue(result)

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    def test_should_not_send_alert(self, mock_get_from_prometheus):
        mock_get_from_prometheus.return_value = {
            "data": {
                "result": [
                    {"values": [["timestamp", str(self.get_timestamps_minus_hours(2))]]}
                ]
            }
        }

        result = self.metrics_gen.should_send_alert("cam1", "site1")
        self.assertFalse(result)

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    @patch("metrics.MetricsGenerator.post_to_prometheus")
    @patch("slack_alert_manager.SlackAlertManager.send_alert")
    def test_check_camera_displacement_no_alert(
        self, mock_send_alert, mock_post, mock_get_from_prometheus
    ):
        one_hour_ago = self.get_timestamps_minus_hours(1)
        two_hours_ago = self.get_timestamps_minus_hours(20)
        mock_get_from_prometheus.side_effect = [
            {"data": {"result": [{"value": [one_hour_ago, 25]}]}},
            {
                "data": {
                    "result": [
                        {
                            "values": [
                                [one_hour_ago, str(one_hour_ago)],
                                [two_hours_ago, str(two_hours_ago)],
                            ]
                        }
                    ]
                }
            },
        ]
        mock_send_alert.return_value = "Alert Sent"
        mock_post.return_value = {"statusCode": 200, "body": "OK"}

        result = self.metrics_gen.check_camera_displacement(
            {
                "camera_id": "cam1",
                "site_id": "site1",
                "status": "Rejected",
                "ref_img_path": "path1",
                "captured_img_path": "path2",
                "roi_types": [],
                "roi_ids": [],
            }
        )
        self.assertTrue(result)
        assert not mock_send_alert.called

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    @patch("metrics.MetricsGenerator.post_to_prometheus")
    @patch("slack_alert_manager.SlackAlertManager.send_alert")
    def test_check_camera_displacement_alert(
        self, mock_send_alert, mock_post, mock_get_from_prometheus
    ):
        timestamp1 = self.get_timestamps_minus_hours(25)
        timestamp2 = self.get_timestamps_minus_hours(30)
        mock_get_from_prometheus.side_effect = [
            {"data": {"result": [{"value": [timestamp1, 25]}]}},
            {
                "data": {
                    "result": [
                        {
                            "values": [
                                [timestamp1, str(timestamp1)],
                                [timestamp2, str(timestamp2)],
                            ]
                        }
                    ]
                }
            },
        ]
        mock_send_alert.return_value = "Alert Sent"
        mock_post.return_value = {"statusCode": 200, "body": "OK"}

        result = self.metrics_gen.check_camera_displacement(
            {
                "camera_id": "cam1",
                "site_id": "site1",
                "status": "Rejected",
                "ref_img_path": "path1",
                "captured_img_path": "path2",
                "roi_types": ["order"],
                "roi_ids": ["order"],
            }
        )
        self.assertTrue(result)
        mock_send_alert.assert_called_once()

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    @patch("metrics.MetricsGenerator.post_to_prometheus")
    @patch("slack_alert_manager.SlackAlertManager.send_alert")
    def test_check_camera_displacement_false(
        self, mock_send_alert, mock_post, mock_get_from_prometheus
    ):
        timestamp1 = self.get_timestamps_minus_hours(25)
        timestamp2 = self.get_timestamps_minus_hours(30)
        mock_get_from_prometheus.side_effect = [
            {"data": {"result": [{"value": [timestamp1, 2]}]}},
            {
                "data": {
                    "result": [
                        {
                            "values": [
                                [timestamp1, str(timestamp1)],
                                [timestamp2, str(timestamp2)],
                            ]
                        }
                    ]
                }
            },
        ]
        mock_send_alert.return_value = "Alert Sent"
        mock_post.return_value = {"statusCode": 200, "body": "OK"}

        result = self.metrics_gen.check_camera_displacement(
            {
                "camera_id": "cam1",
                "site_id": "site1",
                "status": "Rejected",
                "ref_img_path": "path1",
                "captured_img_path": "path2",
                "roi_types": [],
                "roi_ids": [],
            }
        )
        self.assertFalse(result)
        assert not mock_send_alert.called


if __name__ == "__main__":
    unittest.main()
