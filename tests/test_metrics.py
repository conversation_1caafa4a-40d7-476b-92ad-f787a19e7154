import os
import unittest
from unittest.mock import patch, MagicMock
import sys
import time

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../src")))
from metrics import MetricsGenerator
from slack_alert_manager import SlackAlertManager


class TestMetricsGenerator(unittest.TestCase):

    def setUp(self):
        os.environ["SLACK_TOKEN"] = "test"

        slack_alert_manager = SlackAlertManager()
        self.metrics_gen = MetricsGenerator(slack_alert_manager)

    @staticmethod
    def get_timestamps_minus_hours(hours=1) -> int:
        current_time = int(time.time())  # Get the current Unix timestamp in seconds
        timestamp = current_time - (hours * 60 * 60)  # Subtract hours
        return timestamp

    @patch("requests.get")
    def test_get_from_prometheus(self, mock_get):
        mock_response = MagicMock()
        mock_response.json.return_value = {"data": "test_data"}
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        response = self.metrics_gen.get_from_prometheus("http://test-url", {})
        self.assertEqual(response, {"data": "test_data"})
        mock_get.assert_called_once()

    @patch("requests.post")
    def test_post_to_prometheus_success(self, mock_post):
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        result = self.metrics_gen.post_to_prometheus("metric_data")
        self.assertEqual(result, {"statusCode": 200, "body": "OK"})
        mock_post.assert_called_once()

    def test_format_prometheus_gauge(self):
        result = self.metrics_gen.format_prometheus_gauge(
            "test_metric", 100, {"tag1": "value1"}
        )
        self.assertEqual(result, 'test_metric{tag1="value1"} 100\n')

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    def test_should_send_alert(self, mock_get_from_prometheus):
        mock_get_from_prometheus.return_value = {
            "data": {"result": [{"values": [["timestamp", "1700000000"]]}]}
        }

        result = self.metrics_gen.should_send_alert("cam1", "site1")
        self.assertTrue(result)

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    def test_should_not_send_alert(self, mock_get_from_prometheus):
        mock_get_from_prometheus.return_value = {
            "data": {
                "result": [
                    {"values": [["timestamp", str(self.get_timestamps_minus_hours(2))]]}
                ]
            }
        }

        result = self.metrics_gen.should_send_alert("cam1", "site1")
        self.assertFalse(result)

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    @patch("metrics.MetricsGenerator.post_to_prometheus")
    @patch("slack_alert_manager.SlackAlertManager.send_alert")
    def test_check_camera_displacement_no_alert(
        self, mock_send_alert, mock_post, mock_get_from_prometheus
    ):
        one_hour_ago = self.get_timestamps_minus_hours(1)
        two_hours_ago = self.get_timestamps_minus_hours(20)
        mock_get_from_prometheus.side_effect = [
            {"data": {"result": [{"value": [one_hour_ago, 25]}]}},
            {
                "data": {
                    "result": [
                        {
                            "values": [
                                [one_hour_ago, str(one_hour_ago)],
                                [two_hours_ago, str(two_hours_ago)],
                            ]
                        }
                    ]
                }
            },
        ]
        mock_send_alert.return_value = "Alert Sent"
        mock_post.return_value = {"statusCode": 200, "body": "OK"}

        result = self.metrics_gen.check_camera_displacement(
            {
                "camera_id": "cam1",
                "site_id": "site1",
                "status": "Rejected",
                "ref_img_path": "path1",
                "captured_img_path": "path2",
                "roi_types": [],
                "roi_ids": [],
            }
        )
        self.assertTrue(result)
        assert not mock_send_alert.called

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    @patch("metrics.MetricsGenerator.post_to_prometheus")
    @patch("slack_alert_manager.SlackAlertManager.send_alert")
    def test_check_camera_displacement_alert(
        self, mock_send_alert, mock_post, mock_get_from_prometheus
    ):
        timestamp1 = self.get_timestamps_minus_hours(25)
        timestamp2 = self.get_timestamps_minus_hours(30)
        mock_get_from_prometheus.side_effect = [
            {"data": {"result": [{"value": [timestamp1, 25]}]}},
            {
                "data": {
                    "result": [
                        {
                            "values": [
                                [timestamp1, str(timestamp1)],
                                [timestamp2, str(timestamp2)],
                            ]
                        }
                    ]
                }
            },
        ]
        mock_send_alert.return_value = "Alert Sent"
        mock_post.return_value = {"statusCode": 200, "body": "OK"}

        result = self.metrics_gen.check_camera_displacement(
            {
                "camera_id": "cam1",
                "site_id": "site1",
                "status": "Rejected",
                "ref_img_path": "path1",
                "captured_img_path": "path2",
                "roi_types": ["order"],
                "roi_ids": ["order"],
            }
        )
        self.assertTrue(result)
        mock_send_alert.assert_called_once()

    @patch("metrics.MetricsGenerator.get_from_prometheus")
    @patch("metrics.MetricsGenerator.post_to_prometheus")
    @patch("slack_alert_manager.SlackAlertManager.send_alert")
    def test_check_camera_displacement_false(
        self, mock_send_alert, mock_post, mock_get_from_prometheus
    ):
        timestamp1 = self.get_timestamps_minus_hours(25)
        timestamp2 = self.get_timestamps_minus_hours(30)
        mock_get_from_prometheus.side_effect = [
            {"data": {"result": [{"value": [timestamp1, 2]}]}},
            {
                "data": {
                    "result": [
                        {
                            "values": [
                                [timestamp1, str(timestamp1)],
                                [timestamp2, str(timestamp2)],
                            ]
                        }
                    ]
                }
            },
        ]
        mock_send_alert.return_value = "Alert Sent"
        mock_post.return_value = {"statusCode": 200, "body": "OK"}

        result = self.metrics_gen.check_camera_displacement(
            {
                "camera_id": "cam1",
                "site_id": "site1",
                "status": "Rejected",
                "ref_img_path": "path1",
                "captured_img_path": "path2",
                "roi_types": [],
                "roi_ids": [],
            }
        )
        self.assertFalse(result)
        assert not mock_send_alert.called


class TestVictoriaMetricsClusterMode(unittest.TestCase):
    """Test class for Victoria Metrics cluster mode with separate insert and select endpoints"""

    def setUp(self):
        os.environ["SLACK_TOKEN"] = "test"
        # Set up Victoria Metrics cluster mode environment variables
        os.environ["METRICS_ENDPOINT"] = "https://victoria-metrics.ap-southeast-2.fingermark.tech"
        os.environ["METRICS_USERNAME"] = "ingester"  # Write account
        os.environ["METRICS_PASSWORD"] = "YAXHrbCRg43iPpEJTNbw"
        os.environ["METRICS_READ_USERNAME"] = "querier"  # Read account
        os.environ["METRICS_READ_PASSWORD"] = "read_password_here"

        slack_alert_manager = SlackAlertManager()
        self.metrics_gen = MetricsGenerator(slack_alert_manager)

    def test_cluster_mode_insert_url_construction(self):
        """Test that the insert URL is correctly constructed for cluster mode"""
        expected_insert_url = "https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import"

        # The current implementation constructs the URL as: {endpoint}/api/v1/import/prometheus
        # For cluster mode, it should be: {endpoint}/insert/0/prometheus/api/v1/import
        # We need to verify the URL construction
        self.assertEqual(
            self.metrics_gen.metrics_url,
            "https://victoria-metrics.ap-southeast-2.fingermark.tech/api/v1/import/prometheus"
        )

        # This test shows the current behavior - we'll need to update the MetricsGenerator
        # to handle cluster mode URLs properly

    def test_cluster_mode_select_url_construction(self):
        """Test that the select URL is correctly constructed for cluster mode"""
        expected_select_base = "https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus"

        # For reads, the URL should be: {endpoint}/select/0/prometheus/api/v1/query
        # Current implementation uses: {endpoint}/api/v1/query
        camera_id = "cam1"
        site_id = "site1"
        promql_query = f'sum_over_time(camera_displacement_status{{camera_id="{camera_id}", site_id="{site_id}"}}[24h])'
        query_url = f"{self.metrics_gen.metrics_endpoint}/api/v1/query"

        self.assertEqual(
            query_url,
            "https://victoria-metrics.ap-southeast-2.fingermark.tech/api/v1/query"
        )

        # This test shows current behavior - needs update for cluster mode

    @patch("requests.post")
    def test_cluster_mode_post_to_prometheus_with_ingester_credentials(self, mock_post):
        """Test posting metrics using ingester credentials for cluster mode"""
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        metric_data = 'test_metric{tag1="value1"} 100\n'
        result = self.metrics_gen.post_to_prometheus(metric_data)

        # Verify the request was made with ingester credentials
        mock_post.assert_called_once_with(
            self.metrics_gen.metrics_url,
            data=metric_data,
            headers={"Content-Type": "application/openmetrics-text; version=1.0.0; charset=utf-8"},
            auth=("ingester", "YAXHrbCRg43iPpEJTNbw"),
            timeout=60,
            verify=False,
        )
        self.assertEqual(result, {"statusCode": 200, "body": "OK"})

    @patch("requests.get")
    def test_cluster_mode_get_from_prometheus_with_querier_credentials(self, mock_get):
        """Test querying metrics using querier credentials for cluster mode"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": {"result": [{"value": ["1234567890", "25"]}]}
        }
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        # For cluster mode, we should use querier credentials for reads
        # This test shows what should happen when we implement proper cluster mode support
        query_url = "https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query"
        params = {"query": 'sum_over_time(camera_displacement_status{camera_id="cam1"}[24h])'}

        # Currently this will use ingester credentials, but should use querier credentials
        response = self.metrics_gen.get_from_prometheus(query_url, params)

        mock_get.assert_called_once_with(
            query_url,
            params=params,
            auth=("ingester", "YAXHrbCRg43iPpEJTNbw"),  # Current behavior
            timeout=60,
            verify=False,
        )
        self.assertEqual(response, {"data": {"result": [{"value": ["1234567890", "25"]}]}})

    @patch("requests.get")
    def test_cluster_mode_should_send_alert_with_correct_endpoint(self, mock_get):
        """Test should_send_alert method uses correct cluster mode endpoint"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": {"result": [{"values": [["1700000000", "1700000000"]]}]}
        }
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        result = self.metrics_gen.should_send_alert("cam1", "site1")

        # Verify the correct query_range endpoint was called
        expected_url = f"{self.metrics_gen.metrics_endpoint}/api/v1/query_range"
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        self.assertEqual(call_args[0][0], expected_url)
        self.assertTrue(result)

    @patch("requests.get")
    def test_cluster_mode_check_camera_displacement_with_correct_endpoint(self, mock_get):
        """Test check_camera_displacement method uses correct cluster mode endpoint"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": {"result": [{"value": ["1234567890", "2"]}]}
        }
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        results = {
            "camera_id": "cam1",
            "site_id": "site1",
            "status": "Rejected",
            "ref_img_path": "path1",
            "captured_img_path": "path2",
            "roi_types": [],
            "roi_ids": [],
        }

        result = self.metrics_gen.check_camera_displacement(results)

        # Verify the correct query endpoint was called
        expected_url = f"{self.metrics_gen.metrics_endpoint}/api/v1/query"
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        self.assertEqual(call_args[0][0], expected_url)
        self.assertFalse(result)  # Should be False because displacement_sum (2) < alert_threshold (24)

    def test_environment_variable_configuration(self):
        """Test that environment variables are correctly configured for cluster mode"""
        self.assertEqual(
            self.metrics_gen.metrics_endpoint,
            "https://victoria-metrics.ap-southeast-2.fingermark.tech"
        )
        self.assertEqual(self.metrics_gen.username, "ingester")
        self.assertEqual(self.metrics_gen.password, "YAXHrbCRg43iPpEJTNbw")

    @staticmethod
    def get_timestamps_minus_hours(hours=1) -> int:
        current_time = int(time.time())
        timestamp = current_time - (hours * 60 * 60)
        return timestamp


if __name__ == "__main__":
    unittest.main()
