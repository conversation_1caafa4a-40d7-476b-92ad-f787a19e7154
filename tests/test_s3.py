import unittest
from unittest.mock import patch, MagicMock
import os
import sys

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../src")))
from s3 import upload_image_to_s3


class TestUploadImageToS3(unittest.TestCase):
    @patch("boto3.client")
    @patch("requests.get")
    def test_upload_image_success(self, mock_get, mock_s3_client):
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"image_data"
        mock_get.return_value = mock_response

        mock_s3 = MagicMock()
        mock_s3_client.return_value = mock_s3

        upload_image_to_s3(
            "https://presignedurldemo.s3.eu-west-2.amazonaws.com/image.png?X-Amz-Algorithm=",
            "https://presignedurldemo2.s3.eu-west-2.amazonaws.com/image2.png?X-Amz-Algorithm=",
        )

        mock_s3.put_object.assert_called_once_with(
            Bucket="presignedurldemo", Key="image.png", Body=b"image_data"
        )

    @patch("boto3.client")
    @patch("requests.get")
    def test_upload_image_failure_s3_error(self, mock_get, mock_s3_client):
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.content = b"image_data"
        mock_get.return_value = mock_response

        mock_s3 = MagicMock()
        mock_s3.put_object.side_effect = Exception("S3 upload failed")
        mock_s3_client.return_value = mock_s3

        with self.assertRaises(Exception):
            upload_image_to_s3(
                "https://presignedurldemo.s3.eu-west-2.amazonaws.com/image.png?X-Amz-Algorithm=",
                "https://presignedurldemo2.s3.eu-west-2.amazonaws.com/image2.png?X-Amz-Algorithm=",
            )


if __name__ == "__main__":
    unittest.main()
