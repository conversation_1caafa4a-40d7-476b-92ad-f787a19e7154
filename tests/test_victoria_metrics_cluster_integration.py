#!/usr/bin/env python3
"""
Integration test for Victoria Metrics cluster mode with the new URL:
https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import

This test demonstrates the proper endpoint mapping and credential usage for Victoria Metrics cluster mode:
- Insert (write) operations: /insert/0/prometheus/api/v1/import with ingester credentials
- Select (read) operations: /select/0/prometheus/api/v1/query* with querier credentials
"""

import os
import unittest
from unittest.mock import patch, MagicMock, Mock
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../src"))

# Mock external dependencies to avoid import issues
sys.modules['slack_sdk'] = MagicMock()
sys.modules['slack_sdk.WebClient'] = MagicMock()

from metrics import MetricsGenerator


class TestVictoriaMetricsClusterIntegration(unittest.TestCase):
    """
    Integration test for Victoria Metrics cluster mode with proper endpoint mapping.
    
    Victoria Metrics cluster mode requires:
    1. Insert endpoint: https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import
    2. Select endpoint: https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query*
    3. Ingester credentials for writes, querier credentials for reads
    """

    def setUp(self):
        """Set up test environment with Victoria Metrics cluster mode configuration"""
        os.environ["SLACK_TOKEN"] = "test_token"
        os.environ["SLACK_CHANNEL_ID"] = "test_channel"
        
        # Victoria Metrics cluster mode configuration
        os.environ["METRICS_ENDPOINT"] = "https://victoria-metrics.ap-southeast-2.fingermark.tech"
        os.environ["METRICS_USERNAME"] = "ingester"  # Write account
        os.environ["METRICS_PASSWORD"] = "YAXHrbCRg43iPpEJTNbw"
        os.environ["METRICS_READ_USERNAME"] = "querier"  # Read account  
        os.environ["METRICS_READ_PASSWORD"] = "n6UYZvbG2zZ7MrfdGp7q"

        # Create metrics generator with mock slack manager
        slack_manager = MagicMock()
        self.metrics_gen = MetricsGenerator(slack_manager)

    def test_current_vs_expected_urls(self):
        """Test showing current URL construction vs expected cluster mode URLs"""
        print("\n=== Victoria Metrics Cluster Mode URL Analysis ===")
        
        # Current URLs
        current_insert_url = self.metrics_gen.metrics_url
        current_query_url = f"{self.metrics_gen.metrics_endpoint}/api/v1/query"
        current_query_range_url = f"{self.metrics_gen.metrics_endpoint}/api/v1/query_range"
        
        # Expected cluster mode URLs
        expected_insert_url = "https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import"
        expected_query_url = "https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query"
        expected_query_range_url = "https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query_range"
        
        print(f"Current insert URL:    {current_insert_url}")
        print(f"Expected insert URL:   {expected_insert_url}")
        print(f"URLs match: {current_insert_url == expected_insert_url}")
        print()
        
        print(f"Current query URL:     {current_query_url}")
        print(f"Expected query URL:    {expected_query_url}")
        print(f"URLs match: {current_query_url == expected_query_url}")
        print()
        
        print(f"Current query_range URL: {current_query_range_url}")
        print(f"Expected query_range URL: {expected_query_range_url}")
        print(f"URLs match: {current_query_range_url == expected_query_range_url}")
        
        # Document what needs to be changed
        self.assertNotEqual(current_insert_url, expected_insert_url, 
                           "Insert URL needs to be updated for cluster mode")
        self.assertNotEqual(current_query_url, expected_query_url,
                           "Query URL needs to be updated for cluster mode")

    @patch("requests.post")
    def test_insert_operation_with_cluster_endpoint(self, mock_post):
        """Test metric insertion with the correct cluster mode endpoint"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        # Test metric data
        test_metric = 'camera_displacement_status{camera_id="test_cam",site_id="test_site"} 1\n'
        
        # Execute insert operation
        result = self.metrics_gen.post_to_prometheus(test_metric)

        # Verify the call was made
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        
        # Check URL (currently incorrect for cluster mode)
        actual_url = call_args[0][0]
        expected_url = "https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import"
        
        print(f"\nInsert operation URL test:")
        print(f"Actual URL:   {actual_url}")
        print(f"Expected URL: {expected_url}")
        
        # Check credentials (should be ingester)
        auth = call_args[1]['auth']
        self.assertEqual(auth, ("ingester", "YAXHrbCRg43iPpEJTNbw"))
        
        # Check headers
        headers = call_args[1]['headers']
        self.assertIn("Content-Type", headers)
        
        # Check result
        self.assertEqual(result, {"statusCode": 200, "body": "OK"})

    @patch("requests.get")
    def test_query_operation_with_cluster_endpoint(self, mock_get):
        """Test metric querying with the correct cluster mode endpoint"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": {"result": [{"value": ["1234567890", "10"]}]}
        }
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        # Test query parameters
        query_url = f"{self.metrics_gen.metrics_endpoint}/api/v1/query"
        params = {"query": 'sum_over_time(camera_displacement_status{camera_id="test_cam"}[24h])'}
        
        # Execute query operation
        response = self.metrics_gen.get_from_prometheus(query_url, params)

        # Verify the call was made
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        
        # Check URL (currently incorrect for cluster mode)
        actual_url = call_args[0][0]
        expected_url = "https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query"
        
        print(f"\nQuery operation URL test:")
        print(f"Actual URL:   {actual_url}")
        print(f"Expected URL: {expected_url}")
        
        # Check credentials (currently uses ingester, should use querier for reads)
        auth = call_args[1]['auth']
        print(f"Current auth: {auth}")
        print(f"Expected auth for reads: ('querier', 'n6UYZvbG2zZ7MrfdGp7q')")
        
        # Verify response
        self.assertEqual(response, {"data": {"result": [{"value": ["1234567890", "10"]}]}})

    def test_credentials_configuration(self):
        """Test that credentials are properly configured"""
        print(f"\nCredentials configuration:")
        print(f"Write username: {self.metrics_gen.username}")
        print(f"Write password: {self.metrics_gen.password}")
        print(f"Read username (from env): {os.environ.get('METRICS_READ_USERNAME')}")
        print(f"Read password (from env): {os.environ.get('METRICS_READ_PASSWORD')}")
        
        # Current implementation only supports single set of credentials
        self.assertEqual(self.metrics_gen.username, "ingester")
        self.assertEqual(self.metrics_gen.password, "YAXHrbCRg43iPpEJTNbw")

    def test_metric_formatting_for_cluster_mode(self):
        """Test that metric formatting works correctly for cluster mode"""
        metric = self.metrics_gen.format_prometheus_gauge(
            "camera_displacement_alert",
            1234567890,
            {"camera_id": "test_cam", "site_id": "test_site"}
        )
        
        expected = 'camera_displacement_alert{camera_id="test_cam",site_id="test_site"} 1234567890\n'
        self.assertEqual(metric, expected)
        print(f"\nFormatted metric: {metric.strip()}")

    def test_environment_variables_setup(self):
        """Test environment variables are correctly set for cluster mode"""
        self.assertEqual(
            os.environ["METRICS_ENDPOINT"],
            "https://victoria-metrics.ap-southeast-2.fingermark.tech"
        )
        self.assertEqual(os.environ["METRICS_USERNAME"], "ingester")
        self.assertEqual(os.environ["METRICS_READ_USERNAME"], "querier")

    def print_cluster_mode_requirements(self):
        """Print the requirements for proper cluster mode implementation"""
        print("\n=== Victoria Metrics Cluster Mode Requirements ===")
        print("1. Insert operations:")
        print("   URL: https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import")
        print("   Credentials: ingester / YAXHrbCRg43iPpEJTNbw")
        print()
        print("2. Select operations:")
        print("   URL: https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query*")
        print("   Credentials: querier / n6UYZvbG2zZ7MrfdGp7q")
        print()
        print("3. Required changes to MetricsGenerator:")
        print("   - Update metrics_url construction for insert endpoint")
        print("   - Update query URL construction for select endpoint")
        print("   - Add support for separate read credentials")
        print("   - Modify get_from_prometheus to use querier credentials")


if __name__ == "__main__":
    # Run the requirements printer first
    test_instance = TestVictoriaMetricsClusterIntegration()
    test_instance.setUp()
    test_instance.print_cluster_mode_requirements()
    
    # Run the actual tests
    unittest.main(verbosity=2)
