import os
import unittest
from unittest.mock import patch, MagicMock
import sys
import time

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../src")))

# Mock the slack_sdk import to avoid dependency issues in tests
sys.modules['slack_sdk'] = MagicMock()
sys.modules['slack_sdk.WebClient'] = MagicMock()

from metrics import MetricsGenerator


class TestVictoriaMetricsClusterMode(unittest.TestCase):
    """
    Test class for Victoria Metrics cluster mode with proper endpoint mapping:
    - Insert (write) operations: /insert/0/prometheus/api/v1/import
    - Select (read) operations: /select/0/prometheus/api/v1/query*
    - Uses ingester credentials for writes, querier credentials for reads
    """

    def setUp(self):
        os.environ["SLACK_TOKEN"] = "test"
        # Set up Victoria Metrics cluster mode environment variables
        os.environ["METRICS_ENDPOINT"] = "https://victoria-metrics.ap-southeast-2.fingermark.tech"
        os.environ["METRICS_USERNAME"] = "ingester"  # Write account
        os.environ["METRICS_PASSWORD"] = "YAXHrbCRg43iPpEJTNbw"
        os.environ["METRICS_READ_USERNAME"] = "querier"  # Read account
        os.environ["METRICS_READ_PASSWORD"] = "n6UYZvbG2zZ7MrfdGp7q"

        # Create a mock slack alert manager
        slack_alert_manager = MagicMock()
        self.metrics_gen = MetricsGenerator(slack_alert_manager)

    def test_cluster_mode_insert_url_construction(self):
        """Test that the insert URL is correctly constructed for cluster mode"""
        # For Victoria Metrics cluster mode, insert URL should be:
        # https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import
        expected_insert_url = "https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import"
        
        # Test the current URL construction
        actual_url = self.metrics_gen.metrics_url
        
        # This test documents the expected behavior for cluster mode
        # The current implementation may need to be updated
        print(f"Current insert URL: {actual_url}")
        print(f"Expected insert URL: {expected_insert_url}")

    @patch("requests.post")
    def test_cluster_mode_insert_operation(self, mock_post):
        """Test inserting metrics with correct cluster mode endpoint and ingester credentials"""
        mock_response = MagicMock()
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        # Test data
        metric_data = 'camera_displacement_status{camera_id="cam1",site_id="site1"} 1\n'
        
        # Execute the insert operation
        result = self.metrics_gen.post_to_prometheus(metric_data)

        # Verify the request was made correctly
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        
        # Check URL (should be insert endpoint for cluster mode)
        called_url = call_args[0][0]
        print(f"Called URL: {called_url}")
        
        # Check authentication (should use ingester credentials)
        auth = call_args[1]['auth']
        self.assertEqual(auth, ("ingester", "YAXHrbCRg43iPpEJTNbw"))
        
        # Check data
        self.assertEqual(call_args[1]['data'], metric_data)
        
        # Check result
        self.assertEqual(result, {"statusCode": 200, "body": "OK"})

    @patch("requests.get")
    def test_cluster_mode_select_operation(self, mock_get):
        """Test querying metrics with correct cluster mode endpoint and querier credentials"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": {"result": [{"value": ["1234567890", "25"]}]}
        }
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        # For cluster mode, select URL should be:
        # https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query
        query_url = f"{self.metrics_gen.metrics_endpoint}/select/0/prometheus/api/v1/query"
        params = {"query": 'sum_over_time(camera_displacement_status{camera_id="cam1"}[24h])'}
        
        # Execute the query operation
        response = self.metrics_gen.get_from_prometheus(query_url, params)
        
        # Verify the request was made correctly
        mock_get.assert_called_once_with(
            query_url,
            params=params,
            auth=("ingester", "YAXHrbCRg43iPpEJTNbw"),  # Current implementation uses write credentials
            timeout=60,
            verify=False,
        )
        
        self.assertEqual(response, {"data": {"result": [{"value": ["1234567890", "25"]}]}})

    @patch("requests.get")
    def test_should_send_alert_cluster_mode(self, mock_get):
        """Test should_send_alert method with cluster mode query_range endpoint"""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "data": {"result": [{"values": [["1700000000", "1700000000"]]}]}
        }
        mock_response.raise_for_status = MagicMock()
        mock_get.return_value = mock_response

        result = self.metrics_gen.should_send_alert("cam1", "site1")

        # Verify the query_range endpoint was called
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        called_url = call_args[0][0]
        
        # For cluster mode, this should be:
        # https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query_range
        expected_cluster_url = f"{self.metrics_gen.metrics_endpoint}/select/0/prometheus/api/v1/query_range"
        print(f"Called URL: {called_url}")
        print(f"Expected cluster URL: {expected_cluster_url}")
        
        self.assertTrue(result)

    @patch("requests.get")
    def test_check_camera_displacement_cluster_mode(self, mock_get):
        """Test check_camera_displacement method with cluster mode query endpoint"""
        # Mock responses for both the main query and the should_send_alert query
        mock_responses = [
            # First call: check_camera_displacement query
            MagicMock(json=lambda: {"data": {"result": [{"value": ["1234567890", "25"]}]}}),
            # Second call: should_send_alert query
            MagicMock(json=lambda: {"data": {"result": [{"values": [["1700000000", "1700000000"]]}]}})
        ]

        for mock_resp in mock_responses:
            mock_resp.raise_for_status = MagicMock()

        mock_get.side_effect = mock_responses

        results = {
            "camera_id": "cam1",
            "site_id": "site1",
            "status": "Rejected",
            "ref_img_path": "path1",
            "captured_img_path": "path2",
            "roi_types": [],
            "roi_ids": [],
        }

        result = self.metrics_gen.check_camera_displacement(results)

        # Verify both query endpoints were called
        self.assertEqual(mock_get.call_count, 2)

        # Check first call (main displacement query)
        first_call_args = mock_get.call_args_list[0]
        called_url = first_call_args[0][0]

        # For cluster mode, this should be:
        # https://victoria-metrics.ap-southeast-2.fingermark.tech/select/0/prometheus/api/v1/query
        expected_cluster_url = f"{self.metrics_gen.metrics_endpoint}/select/0/prometheus/api/v1/query"
        print(f"Called URL: {called_url}")
        print(f"Expected cluster URL: {expected_cluster_url}")

        self.assertTrue(result)  # Should be True because displacement_sum (25) >= alert_threshold (24)

    def test_environment_variables_setup(self):
        """Test that environment variables are correctly configured"""
        self.assertEqual(
            self.metrics_gen.metrics_endpoint,
            "https://victoria-metrics.ap-southeast-2.fingermark.tech"
        )
        self.assertEqual(self.metrics_gen.username, "ingester")
        self.assertEqual(self.metrics_gen.password, "YAXHrbCRg43iPpEJTNbw")

    def test_format_prometheus_gauge_cluster_mode(self):
        """Test metric formatting works correctly for cluster mode"""
        result = self.metrics_gen.format_prometheus_gauge(
            "camera_displacement_status", 
            1, 
            {"camera_id": "cam1", "site_id": "site1"}
        )
        expected = 'camera_displacement_status{camera_id="cam1",site_id="site1"} 1\n'
        self.assertEqual(result, expected)

    @staticmethod
    def get_timestamps_minus_hours(hours=1) -> int:
        current_time = int(time.time())
        timestamp = current_time - (hours * 60 * 60)
        return timestamp


if __name__ == "__main__":
    unittest.main()
