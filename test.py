from prometheus_client import <PERSON><PERSON><PERSON><PERSON><PERSON>, Gauge, push_to_gateway
from prometheus_client.exposition import basic_auth_handler


class VictoriaMetricInstance:
    def __init__(self, metrics_endpoint: str, username: str, password: str):
        self.metrics_endpoint = metrics_endpoint
        self.username = username
        self.password = password

    def test_publish(self):
        registry = CollectorRegistry()
        g = Gauge('my_test_metric', 'This is a test metric', registry=registry)
        g.set(123.45)
        push_to_gateway(self.metrics_endpoint, job='test_job', registry=registry, handler=self._auth_handler)

    def _auth_handler(self, url, method, timeout, headers, data):
        return basic_auth_handler(url, method, timeout, headers, data, self.username, self.password)


# Test Old VM (works)
print("Testing old VM endpoint...")
try:
    VictoriaMetricInstance(
        metrics_endpoint="https://ec2-18-208-145-252.compute-1.amazonaws.com:8427",
        username="local-single-node",
        password="my-houst"
    ).test_publish()
    print("SUCCESS: Old VM endpoint worked!")
except Exception as e:
    print(f"ERROR with old VM endpoint: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*50 + "\n")

# Test New VM (corrected endpoint)
print("Testing new VM endpoint...")
try:
    VictoriaMetricInstance(
        metrics_endpoint="https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus/api/v1/import",
        username="ingester",
        password="YAXHrbCRg43iPpEJTNbw"
    ).test_publish()
    print("SUCCESS: New VM endpoint worked!")
except Exception as e:
    print(f"ERROR with new VM endpoint: {e}")
    import traceback
    traceback.print_exc()